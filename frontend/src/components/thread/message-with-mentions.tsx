'use client';

import React from 'react';
import { ToolMentionDisplay } from './tool-mention-chip';

interface MessageWithMentionsProps {
  content: string;
  className?: string;
}

/**
 * Component that renders message content with tool mentions as styled chips and app names as icon chips
 */
export function MessageWithMentions({ content, className }: MessageWithMentionsProps) {
  // Regular expression to match tool mentions in the format @[DisplayName](toolId)
  const mentionRegex = /@\[([^\]]+)\]\(([^)]+)\)/g;

  // App name patterns to detect and convert to chips
  const appPatterns = [
    { pattern: /\bgmail\b/gi, appKey: 'gmail.com', displayName: 'Gmail' },
    { pattern: /\bnotion\b/gi, appKey: 'notion.so', displayName: 'Notion' },
    { pattern: /\blinear\b/gi, appKey: 'linear.app', displayName: 'Linear' },
    { pattern: /\bhubspot\b/gi, appKey: 'hubspot.com', displayName: 'HubSpot' },
    { pattern: /\bfigma\b/gi, appKey: 'figma.com', displayName: 'Figma' },
    { pattern: /\bclickup\b/gi, appKey: 'clickup.com', displayName: 'ClickUp' },
    { pattern: /\btwitter\b/gi, appKey: 'twitter.com', displayName: 'Twitter' },
    { pattern: /\bgoogle\s+sheets?\b/gi, appKey: 'google.com', displayName: 'Google Sheets' },
    { pattern: /\bgoogle\s+docs?\b/gi, appKey: 'googledocs.com', displayName: 'Google Docs' },
    { pattern: /\bmicrosoft\b/gi, appKey: 'microsoft.com', displayName: 'Microsoft' },
    { pattern: /\bapollo\b/gi, appKey: 'apollo.io', displayName: 'Apollo' },
  ];

  // Parse the content and create elements
  const parseContent = (text: string) => {
    const elements: React.ReactNode[] = [];
    let lastIndex = 0;
    let match: RegExpExecArray | null;

    // First, handle tool mentions
    mentionRegex.lastIndex = 0;
    const mentionMatches: Array<{ match: RegExpExecArray; type: 'mention' }> = [];

    while ((match = mentionRegex.exec(text)) !== null) {
      mentionMatches.push({ match, type: 'mention' });
    }

    // Then, find app name matches
    const appMatches: Array<{ match: RegExpExecArray; type: 'app'; appKey: string; displayName: string }> = [];

    appPatterns.forEach(({ pattern, appKey, displayName }) => {
      pattern.lastIndex = 0;
      while ((match = pattern.exec(text)) !== null) {
        appMatches.push({ match, type: 'app', appKey, displayName });
      }
    });

    // Combine and sort all matches by position
    const allMatches = [...mentionMatches, ...appMatches].sort((a, b) => a.match.index - b.match.index);

    // Remove overlapping matches (prioritize tool mentions over app names)
    const filteredMatches = [];
    for (let i = 0; i < allMatches.length; i++) {
      const current = allMatches[i];
      const currentEnd = current.match.index + current.match[0].length;

      // Check if this match overlaps with any previous match
      const hasOverlap = filteredMatches.some(prev => {
        const prevEnd = prev.match.index + prev.match[0].length;
        return (current.match.index < prevEnd && current.match.index >= prev.match.index) ||
               (prev.match.index < currentEnd && prev.match.index >= current.match.index);
      });

      if (!hasOverlap) {
        filteredMatches.push(current);
      }
    }

    // Process filtered matches
    filteredMatches.forEach((item) => {
      const { match, type } = item;
      const [fullMatch] = match;
      const startIndex = match.index;

      // Add text before the match
      if (startIndex > lastIndex) {
        const beforeText = text.slice(lastIndex, startIndex);
        if (beforeText) {
          elements.push(beforeText);
        }
      }

      // Add the appropriate chip
      if (type === 'mention') {
        const [, displayName, toolId] = match;
        elements.push(
          <ToolMentionDisplay
            key={`mention-${startIndex}-${toolId}`}
            toolId={toolId}
            displayName={displayName}
            size="sm"
            className="mx-1"
          />
        );
      } else if (type === 'app') {
        const { appKey, displayName } = item as any;
        // Use the app key as the tool ID for the ToolMentionDisplay component
        elements.push(
          <ToolMentionDisplay
            key={`app-${startIndex}-${appKey}`}
            toolId={`app:${appKey}`}
            displayName={displayName}
            size="sm"
            className="mx-1"
          />
        );
      }

      lastIndex = startIndex + fullMatch.length;
    });

    // Add remaining text after the last mention
    if (lastIndex < text.length) {
      const remainingText = text.slice(lastIndex);
      if (remainingText) {
        elements.push(remainingText);
      }
    }

    // If no mentions were found, return the original text
    if (elements.length === 0) {
      return text;
    }

    return elements;
  };

  const parsedContent = parseContent(content);

  return (
    <div className={className}>
      {Array.isArray(parsedContent) ? (
        parsedContent.map((element, index) => (
          <React.Fragment key={index}>{element}</React.Fragment>
        ))
      ) : (
        parsedContent
      )}
    </div>
  );
}

/**
 * Hook to check if a message contains tool mentions
 */
export function useHasToolMentions(content: string): boolean {
  const mentionRegex = /@\[([^\]]+)\]\(([^)]+)\)/g;
  mentionRegex.lastIndex = 0;
  return mentionRegex.test(content);
}

/**
 * Hook to extract tool mentions from message content
 */
export function useToolMentions(content: string) {
  const mentionRegex = /@\[([^\]]+)\]\(([^)]+)\)/g;
  const mentions: Array<{ displayName: string; toolId: string; startIndex: number }> = [];
  let match: RegExpExecArray | null;

  mentionRegex.lastIndex = 0;
  while ((match = mentionRegex.exec(content)) !== null) {
    const [, displayName, toolId] = match;
    mentions.push({
      displayName,
      toolId,
      startIndex: match.index,
    });
  }

  return mentions;
}
