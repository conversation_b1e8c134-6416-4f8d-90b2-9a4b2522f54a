'use client';

import React from 'react';
import { ToolMentionDisplay } from './tool-mention-chip';

interface MessageWithMentionsProps {
  content: string;
  className?: string;
}

/**
 * Component that renders message content with tool mentions as styled chips and app names as icon chips
 */
export function MessageWithMentions({ content, className }: MessageWithMentionsProps) {
  // Regular expression to match tool mentions in the format @[DisplayName](toolId)
  const mentionRegex = /@\[([^\]]+)\]\(([^)]+)\)/g;

  // App name mapping to detect mentions that should be converted to app chips
  const appNameMapping: Record<string, { appKey: string; displayName: string }> = {
    'gmail': { appKey: 'gmail.com', displayName: 'Gmail' },
    'notion': { appKey: 'notion.so', displayName: 'Notion' },
    'linear': { appKey: 'linear.app', displayName: 'Linear' },
    'hubspot': { appKey: 'hubspot.com', displayName: 'HubSpot' },
    'figma': { appKey: 'figma.com', displayName: 'Figma' },
    'clickup': { appKey: 'clickup.com', displayName: 'ClickUp' },
    'twitter': { appKey: 'twitter.com', displayName: 'Twitter' },
    'google sheets': { appKey: 'google.com', displayName: 'Google Sheets' },
    'google docs': { appKey: 'googledocs.com', displayName: 'Google Docs' },
    'microsoft': { appKey: 'microsoft.com', displayName: 'Microsoft' },
    'apollo': { appKey: 'apollo.io', displayName: 'Apollo' },
  };

  // Parse the content and create elements
  const parseContent = (text: string) => {
    const elements: React.ReactNode[] = [];
    let lastIndex = 0;
    let match: RegExpExecArray | null;

    // Find all mentions in the text
    mentionRegex.lastIndex = 0;

    while ((match = mentionRegex.exec(text)) !== null) {
      const [fullMatch, displayName, toolId] = match;
      const startIndex = match.index;

      // Add text before the mention
      if (startIndex > lastIndex) {
        const beforeText = text.slice(lastIndex, startIndex);
        if (beforeText) {
          elements.push(beforeText);
        }
      }

      // Check if this mention is an app name that should be converted to an app chip
      const lowerDisplayName = displayName.toLowerCase();
      const appInfo = appNameMapping[lowerDisplayName];

      if (appInfo) {
        // This is an app mention, render it with the app icon
        elements.push(
          <ToolMentionDisplay
            key={`app-${startIndex}-${appInfo.appKey}`}
            toolId={`app:${appInfo.appKey}`}
            displayName={appInfo.displayName}
            size="sm"
            className="mx-1"
          />
        );
      } else {
        // This is a regular tool mention
        elements.push(
          <ToolMentionDisplay
            key={`mention-${startIndex}-${toolId}`}
            toolId={toolId}
            displayName={displayName}
            size="sm"
            className="mx-1"
          />
        );
      }

      lastIndex = startIndex + fullMatch.length;
    }

    // Add remaining text after the last mention
    if (lastIndex < text.length) {
      const remainingText = text.slice(lastIndex);
      if (remainingText) {
        elements.push(remainingText);
      }
    }

    // If no mentions were found, return the original text
    if (elements.length === 0) {
      return text;
    }

    return elements;
  };

  const parsedContent = parseContent(content);

  return (
    <div className={className}>
      {Array.isArray(parsedContent) ? (
        parsedContent.map((element, index) => (
          <React.Fragment key={index}>{element}</React.Fragment>
        ))
      ) : (
        parsedContent
      )}
    </div>
  );
}

/**
 * Hook to check if a message contains tool mentions
 */
export function useHasToolMentions(content: string): boolean {
  const mentionRegex = /@\[([^\]]+)\]\(([^)]+)\)/g;
  mentionRegex.lastIndex = 0;
  return mentionRegex.test(content);
}

/**
 * Hook to extract tool mentions from message content
 */
export function useToolMentions(content: string) {
  const mentionRegex = /@\[([^\]]+)\]\(([^)]+)\)/g;
  const mentions: Array<{ displayName: string; toolId: string; startIndex: number }> = [];
  let match: RegExpExecArray | null;

  mentionRegex.lastIndex = 0;
  while ((match = mentionRegex.exec(content)) !== null) {
    const [, displayName, toolId] = match;
    mentions.push({
      displayName,
      toolId,
      startIndex: match.index,
    });
  }

  return mentions;
}
